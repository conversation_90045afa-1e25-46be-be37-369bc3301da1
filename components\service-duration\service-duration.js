Component({
  properties: {
    orderDetail: {
      type: Object,
      value: {}
    },
    serviceDurationRecords: {
      type: Array,
      value: []
    },
    allAdditionalServices: {
      type: Array,
      value: []
    },
    serviceDurationStatistics: {
      type: Object,
      value: {}
    },
    showServiceDuration: {
      type: Boolean,
      value: true
    }
  },

  data: {},

  methods: {
    // 切换服务时长统计显示
    onToggleServiceDuration() {
      this.triggerEvent('toggleServiceDuration');
    },

    // 开始主服务时长统计
    onStartMainService(e) {
      const { orderDetailId, serviceId, serviceName } = e.currentTarget.dataset;
      this.triggerEvent('startMainService', {
        orderDetailId,
        serviceId,
        serviceName
      });
    },

    // 开始增项服务时长统计
    onStartAdditionalService(e) {
      console.log('=== service-duration组件：开始增项服务 ===');
      console.log('点击事件:', e);
      console.log('dataset:', e.currentTarget.dataset);

      // 添加一个简单的提示来确认点击被触发
      wx.showToast({
        title: '按钮被点击了',
        icon: 'none',
        duration: 1000
      });

      const { serviceType, serviceId, additionalServiceId, orderDetailId, serviceName, sequenceNumber } = e.currentTarget.dataset;

      const eventData = {
        serviceType,
        serviceId,
        additionalServiceId,
        orderDetailId,
        serviceName,
        sequenceNumber: parseInt(sequenceNumber) || 1
      };

      console.log('触发事件数据:', eventData);

      this.triggerEvent('startAdditionalService', eventData);
    },

    // 结束服务时长统计
    onEndServiceDuration(e) {
      const { recordId, serviceName } = e.currentTarget.dataset;
      this.triggerEvent('endServiceDuration', {
        recordId,
        serviceName
      });
    }
  }
});
